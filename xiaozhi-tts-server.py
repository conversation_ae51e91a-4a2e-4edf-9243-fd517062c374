# xiaozhi-tts-server.py
# 小智AI MCP接入点 - TTS服务器
# 基于飞书文档要求实现的WebSocket MCP服务器

from mcp.server.fastmcp import FastMCP
import logging
import asyncio
import websockets
import json
import os
import tempfile
import subprocess
from openai import OpenAI
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('xiaozhi_tts_mcp')

# 创建MCP服务器实例
mcp = FastMCP("XiaozhiTTS")

# OpenAI客户端
openai_client = None

def initialize_openai_client():
    """初始化OpenAI客户端"""
    global openai_client
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        raise ValueError("OPENAI_API_KEY环境变量未设置")
    openai_client = OpenAI(api_key=api_key)
    logger.info("OpenAI客户端初始化成功")

@mcp.tool()
def text_to_speech(text: str, voice: str = "alloy", model: str = "tts-1", speed: float = 1.0) -> dict:
    """
    将文本转换为语音并播放。支持多种语音角色和模型。
    
    参数:
    - text: 要转换为语音的文本内容
    - voice: 语音角色 (alloy, echo, fable, onyx, nova, shimmer)
    - model: TTS模型 (tts-1, tts-1-hd)
    - speed: 语音速度 (0.25-4.0)
    
    使用场景: 当用户需要将文本转换为语音时使用此工具
    """
    try:
        if not openai_client:
            initialize_openai_client()
        
        logger.info(f"开始TTS转换: text='{text[:50]}...', voice={voice}, model={model}, speed={speed}")
        
        # 验证参数
        valid_voices = ["alloy", "echo", "fable", "onyx", "nova", "shimmer"]
        valid_models = ["tts-1", "tts-1-hd"]
        
        if voice not in valid_voices:
            voice = "alloy"
        if model not in valid_models:
            model = "tts-1"
        if not (0.25 <= speed <= 4.0):
            speed = 1.0
        
        # 调用OpenAI TTS API
        response = openai_client.audio.speech.create(
            model=model,
            voice=voice,
            input=text,
            speed=speed,
            response_format="mp3"
        )
        
        # 保存到临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=".mp3") as temp_file:
            temp_file.write(response.content)
            temp_path = temp_file.name
        
        logger.info(f"音频文件已生成: {temp_path}")
        
        # 尝试播放音频（如果系统支持）
        try:
            # 在macOS上使用afplay
            if os.system("which afplay > /dev/null 2>&1") == 0:
                subprocess.run(["afplay", temp_path], check=True)
            # 在Linux上使用aplay或paplay
            elif os.system("which aplay > /dev/null 2>&1") == 0:
                subprocess.run(["aplay", temp_path], check=True)
            elif os.system("which paplay > /dev/null 2>&1") == 0:
                subprocess.run(["paplay", temp_path], check=True)
            else:
                logger.warning("未找到音频播放器，音频文件已生成但无法播放")
        except subprocess.CalledProcessError as e:
            logger.warning(f"音频播放失败: {e}")
        finally:
            # 清理临时文件
            try:
                os.unlink(temp_path)
            except:
                pass
        
        return {
            "success": True,
            "message": f"成功将文本转换为语音并播放",
            "text_length": len(text),
            "voice": voice,
            "model": model,
            "speed": speed
        }
        
    except Exception as e:
        logger.error(f"TTS转换失败: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "message": "文本转语音失败，请检查输入参数和API配置"
        }

@mcp.tool()
def get_available_voices() -> dict:
    """
    获取可用的语音角色列表
    
    使用场景: 当用户想了解有哪些语音角色可选择时使用
    """
    voices = {
        "alloy": "中性、平衡的声音",
        "echo": "男性声音",
        "fable": "英式口音",
        "onyx": "深沉的男性声音",
        "nova": "年轻女性声音",
        "shimmer": "柔和女性声音"
    }
    
    return {
        "success": True,
        "voices": voices,
        "message": "可用语音角色列表"
    }

@mcp.tool()
def get_tts_models() -> dict:
    """
    获取可用的TTS模型列表
    
    使用场景: 当用户想了解有哪些TTS模型可选择时使用
    """
    models = {
        "tts-1": "标准质量，速度较快",
        "tts-1-hd": "高质量，速度较慢"
    }
    
    return {
        "success": True,
        "models": models,
        "message": "可用TTS模型列表"
    }

# WebSocket连接处理
class XiaozhiMCPHandler:
    def __init__(self):
        self.websocket = None
        self.connected = False
    
    async def connect_to_xiaozhi(self, endpoint_url):
        """连接到小智AI MCP接入点"""
        try:
            logger.info(f"正在连接到小智AI MCP接入点: {endpoint_url}")
            self.websocket = await websockets.connect(endpoint_url)
            self.connected = True
            logger.info("成功连接到小智AI MCP接入点")
            
            # 开始处理消息
            await self.handle_messages()
            
        except Exception as e:
            logger.error(f"连接失败: {str(e)}")
            self.connected = False
    
    async def handle_messages(self):
        """处理来自小智AI的消息"""
        try:
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    logger.info(f"收到消息: {data}")
                    
                    # 处理MCP请求
                    response = await self.process_mcp_request(data)
                    
                    # 发送响应
                    if response:
                        await self.websocket.send(json.dumps(response))
                        
                except json.JSONDecodeError:
                    logger.error(f"无法解析消息: {message}")
                except Exception as e:
                    logger.error(f"处理消息时出错: {str(e)}")
                    
        except websockets.exceptions.ConnectionClosed:
            logger.info("WebSocket连接已关闭")
            self.connected = False
        except Exception as e:
            logger.error(f"消息处理循环出错: {str(e)}")
            self.connected = False
    
    async def process_mcp_request(self, request):
        """处理MCP请求"""
        try:
            request_type = request.get('method', '')
            
            if request_type == 'tools/list':
                # 返回工具列表
                return {
                    "jsonrpc": "2.0",
                    "id": request.get('id'),
                    "result": {
                        "tools": [
                            {
                                "name": "text_to_speech",
                                "description": "将文本转换为语音并播放",
                                "inputSchema": {
                                    "type": "object",
                                    "properties": {
                                        "text": {"type": "string", "description": "要转换的文本"},
                                        "voice": {"type": "string", "description": "语音角色", "default": "alloy"},
                                        "model": {"type": "string", "description": "TTS模型", "default": "tts-1"},
                                        "speed": {"type": "number", "description": "语音速度", "default": 1.0}
                                    },
                                    "required": ["text"]
                                }
                            },
                            {
                                "name": "get_available_voices",
                                "description": "获取可用的语音角色列表",
                                "inputSchema": {"type": "object", "properties": {}}
                            },
                            {
                                "name": "get_tts_models",
                                "description": "获取可用的TTS模型列表",
                                "inputSchema": {"type": "object", "properties": {}}
                            }
                        ]
                    }
                }
            
            elif request_type == 'tools/call':
                # 调用工具
                tool_name = request.get('params', {}).get('name')
                arguments = request.get('params', {}).get('arguments', {})
                
                if tool_name == 'text_to_speech':
                    result = text_to_speech(**arguments)
                elif tool_name == 'get_available_voices':
                    result = get_available_voices()
                elif tool_name == 'get_tts_models':
                    result = get_tts_models()
                else:
                    result = {"success": False, "error": f"未知工具: {tool_name}"}
                
                return {
                    "jsonrpc": "2.0",
                    "id": request.get('id'),
                    "result": {
                        "content": [
                            {
                                "type": "text",
                                "text": json.dumps(result, ensure_ascii=False)
                            }
                        ]
                    }
                }
            
            else:
                logger.warning(f"未知请求类型: {request_type}")
                return None
                
        except Exception as e:
            logger.error(f"处理MCP请求时出错: {str(e)}")
            return {
                "jsonrpc": "2.0",
                "id": request.get('id'),
                "error": {
                    "code": -1,
                    "message": str(e)
                }
            }

# 主函数
async def main():
    """主函数"""
    # 初始化OpenAI客户端
    try:
        initialize_openai_client()
    except Exception as e:
        logger.error(f"初始化失败: {e}")
        return
    
    # 获取MCP接入点URL
    endpoint_url = os.getenv('MCP_ENDPOINT')
    if not endpoint_url:
        logger.error("MCP_ENDPOINT环境变量未设置")
        logger.info("请设置环境变量: export MCP_ENDPOINT=<your_mcp_endpoint>")
        return
    
    # 创建处理器并连接
    handler = XiaozhiMCPHandler()
    await handler.connect_to_xiaozhi(endpoint_url)

if __name__ == "__main__":
    # 运行服务器
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("服务器已停止")
    except Exception as e:
        logger.error(f"服务器运行出错: {e}")
