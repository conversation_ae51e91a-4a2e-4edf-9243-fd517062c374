{"name": "tts-mcp", "version": "1.1.1", "description": "OpenAI Text to Speech APIを活用したコマンドラインツールとMCPサーバー", "publishConfig": {"access": "public"}, "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "bin": {"tts-mcp": "./dist/bin/tts-mcp.js", "tts-mcp-server": "./dist/bin/tts-mcp-server.js"}, "files": ["dist", "README.md", "LICENSE"], "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc -p tsconfig.json", "start": "npm run build && node dist/bin/tts-mcp.js", "server": "npm run build && node dist/bin/tts-mcp-server.js", "dev:start": "ts-node bin/tts-mcp.ts", "dev:server": "ts-node bin/tts-mcp-server.ts", "test": "jest --config jest.config.js", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "clean": "<PERSON><PERSON><PERSON> dist", "prepare": "npm run build", "prepublishOnly": "npm run test && npm run build", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix"}, "keywords": ["openai", "tts", "text-to-speech", "cli", "mcp"], "author": "", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0", "commander": "^11.1.0", "dotenv": "^16.3.1", "openai": "^4.20.0", "play-sound": "^1.1.5", "tmp": "^0.2.1", "zod": "^3.22.4"}, "engines": {"node": ">=16.0.0"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/jest": "^29.5.14", "@types/mock-fs": "^4.13.4", "@types/node": "^22.13.11", "@types/play-sound": "^1.1.2", "@types/sinon": "^17.0.4", "@types/supertest": "^6.0.2", "@types/tmp": "^0.2.6", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "eslint": "^8.57.1", "jest": "^29.7.0", "mock-fs": "^5.5.0", "nock": "^14.0.1", "rimraf": "^6.0.1", "sinon": "^19.0.4", "supertest": "^7.1.0", "ts-jest": "^29.2.6", "ts-node": "^10.9.2", "typescript": "^5.8.2"}}